# 朋友圈点赞评论动画功能实现说明

## 功能概述
实现了类似微信朋友圈的点赞评论展开/收起动画效果：
- 点击朋友圈项目右侧的工具图标（icon_tools.png）时，显示点赞评论组件
- 常规状态下不显示点赞评论组件
- 点击其他地方时自动收起已展开的组件
- 使用从右往左的滑动动画效果

## 主要修改内容

### 1. 状态管理变量
在 `_MomentsPageState` 类中添加了以下变量：
```dart
// 每个朋友圈项目的展开状态管理
final Map<String, bool> _expandedStates = {};
final Map<String, AnimationController> _animationControllers = {};
final Map<String, Animation<Offset>> _slideAnimations = {};
```

### 2. 动画控制器管理
- `_getAnimationController(String momentId)`: 获取或创建动画控制器
- `_toggleMomentExpanded(String momentId)`: 切换朋友圈项目的展开状态
- `_closeAllExpandedMoments({String? exceptId})`: 关闭所有展开的朋友圈项目

### 3. UI组件修改
- 将工具图标包装在 `GestureDetector` 中，添加点击事件
- 创建 `_buildAnimatedActionRow` 方法构建带动画的点赞评论组件
- 使用 `SlideTransition` 实现从右往左的滑动动画

### 4. 事件处理
- 点击工具图标时触发展开/收起动画
- 点击页面其他地方时关闭所有展开的组件
- 防止点击动画组件时意外关闭

## 动画效果
- 动画时长：250毫秒
- 动画曲线：`Curves.easeInOut`
- 滑动方向：从右往左（Offset(1.0, 0.0) → Offset.zero）
- 支持多个朋友圈项目独立展开/收起

## 使用方式
1. 点击朋友圈项目右侧的工具图标展开点赞评论组件
2. 再次点击工具图标或点击页面其他地方收起组件
3. 同时只能展开一个朋友圈项目的点赞评论组件

## 技术特点
- 使用 Map 管理每个朋友圈项目的独立状态
- 动画控制器自动创建和销毁
- 防止事件冒泡，确保点击行为正确
- 内存管理：在 dispose 方法中清理所有动画控制器
